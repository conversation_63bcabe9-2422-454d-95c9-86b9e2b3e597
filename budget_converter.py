#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预算数据转换器 - 基于已知的映射关系生成INSERT语句
"""

# 预算文件项目名称到指标名称的映射
budget_project_mapping = {
    # 收入类
    "一、营业总收入": "营业总收入",
    "其中：主营业务收入": "主营业务收入",
    "其他业务收入": "其他业务收入",

    # 成本费用类
    "二、营业总成本": "营业总成本",
    "其中：主营业务成本": "主营业务成本",
    "其他业务成本": "其他业务成本",
    "税金及附加": "税金及附加",
    "其中：主营业务税金及附加": "主营业务税金及附加",
    "销售费用": "销售费用",
    "管理费用": "管理费用",
    "财务费用": "财务费用",
    "资产减值损失": "资产减值损失",
    "其中：坏账减值损失": "坏账减值损失",
    "信用减值损失": "信用减值损失",

    # 投资收益类
    "加：公允价值变动收益（损失以\"-\"号填列）": "公允价值变动收益",
    "加：投资收益（损失以\"-\"号填列）": "投资收益",
    "其中：对联营企业投资收益": "对联营企业投资收益",
    "加：资产处置收益": "资产处置收益",
    "加：其他收益": "其他收益",

    # 利润类
    "三、营业利润（亏损以\"-\"号填列）": "营业利润",
    "加：营业外收入": "营业外收入",
    "其中：非流动资产处置利得": "非流动资产处置利得",
    "减：营业外支出": "营业外支出",
    "其中：非流动资产处置损失": "非流动资产处置损失",
    "四、利润总额（亏损总额以\"-\"号填列）": "利润总额",
    "减：所得税费用": "所得税费用",
    "五、净利润（净亏损以\"-\"号填列）": "净利润",

    # 税费类
    "六、应交税费合计": "应交税费合计",
    "其中：应交增值税": "应交增值税",
    "应交消费税": "应交消费税",
    "应交城建税": "应交城建税",
    "应交教育税": "应交教育税",
    "应交地方教育费附加": "应交地方教育费附加",
    "应交地方水利建设基金附加": "应交地方水利建设基金附加",
    "应交所得税": "应交所得税",
    "其他税费（所得税和地方水利建设基金附加）": "其他税费",

    # 综合指标
    "七、税利合计": "税利合计",
    "八、主要经营指标汇总": "主要经营指标汇总",
    "九、主要经营指标(收支)": "主要经营指标收支",
    "十、坏账准备增加(减)": "坏账准备增加",

    # 财务比率指标
    "管理费用率(%)": "管理费用率",
    "成本费用利润率(%)": "成本费用利润率",
    "销售毛利率(%)": "销售毛利率",
    "销售不含税单价(元/件)": "销售不含税单价",
    "销售含税单价(元/件)": "销售含税单价",
    "销售税费(元/件)": "销售税费单价",
    "销售成本(元/件)": "销售成本单价",
    "销售税利(元/件)": "销售税利单价",
    "销售(管)费用(元/件)": "销售管理费用单价",
    "销售(管)毛利(元/件)": "销售管理毛利单价"
}

# 基于税利指标明细表数据模板.csv的映射
template2_mapping = {
    "增值税（应交增值税-未交增值税贷方累计）": "增值税（应交增值税-未交增值税贷方累计）",
    "消费税": "消费税",
    "城市维护建设税": "城市维护建设税",
    "车船使用税": "车船使用税",
    "印花税": "印花税",
    "土地使用税": "土地使用税",
    "房产税": "房产税",
    "教育费附加": "教育费附加",
    "地方教育费附加": "地方教育费附加",
    "政府性基金": "政府性基金",
    "合计（不含政府性基金）": "合计（不含政府性基金）",
    "应交企业所得税": "应交企业所得税",
    "应交税金合计": "应交税金合计",
    "利润总额(快报第88行)": "利润总额(税利)",
    "加：应交税金合计（快报第95行）": "应交税金合计（税利）",
    "减：应交所得税（快报第93行）": "应交所得税",
    "税利合计": "税利合计",
    "净利润（快报第82行）": "净利润",
    "税金": "税金"
}

# 基于yjzb_indicator.sql的指标名称到ID映射
indicator_id_mapping = {
    # 收入类指标
    "营业总收入": 1952675507478275178,
    "营业收入": 1952675507478275179,
    "主营业务收入": 1952675507478275180,
    "其他业务收入": 1952675507478275181,

    # 成本费用类指标
    "营业总成本": 1952675507478275186,
    "营业成本": 1952675507478275187,
    "主营业务成本": 1952675507478275188,
    "其他业务成本": 1952675507478275189,

    # 销售费用明细
    "办公费（销售费用）": 1952675507458174978,
    "业务招待费（销售费用）": 1952675507458174979,
    "会议费（销售费用）": 1952675507458174980,
    "职工薪酬（销售费用）": 1952675507458174981,
    "职工薪酬-短期薪酬（销售费用）": 1952675507458174982,
    "职工薪酬-短期薪酬-工资（销售费用）": 1952675507458174983,
    "职工薪酬-短期薪酬-员工福利（销售费用）": 1952675507458174984,
    "职工薪酬-短期薪酬-医疗保险（含生育保险）（销售费用）": 1952675507458174985,
    "职工薪酬-短期薪酬-工伤保险（销售费用）": 1952675507458174986,
    "职工薪酬-短期薪酬-生育保险（销售费用）": 1952675507458174987,
    "职工薪酬-短期薪酬-补充医疗保险（销售费用）": 1952675507458174988,
    "职工薪酬-短期薪酬-住房公积金（销售费用）": 1952675507458174989,
    "职工薪酬-短期薪酬-工会经费（销售费用）": 1952675507458174990,
    "职工薪酬-短期薪酬-职工教育经费（销售费用）": 1952675507458174991,
    "职工薪酬-短期薪酬-其他（销售费用）": 1952675507458174992,
    "职工薪酬-离职后福利（销售费用）": 1952675507458174993,
    "职工薪酬-离职后福利-养老保险（销售费用）": 1952675507458174994,
    "职工薪酬-离职后福利-企业年金（销售费用）": 1952675507458174995,
    "职工薪酬-离职后福利-失业保险（销售费用）": 1952675507458174996,
    "职工薪酬-离职后福利-其他（销售费用）": 1952675507458174997,
    "职工薪酬-其他长期职工福利（销售费用）": 1952675507458174998,
    "租赁费（销售费用）": 1952675507458174999,
    "运杂费（销售费用）": 1952675507458175000,
    "装卸费（销售费用）": 1952675507458175001,
    "挑选整理费（销售费用）": 1952675507458175002,
    "包装费（销售费用）": 1952675507458175003,
    "检验费（销售费用）": 1952675507458175004,
    "保管费（销售费用）": 1952675507458175005,
    "保险费（销售费用）": 1952675507458175006,
    "保险费-车辆保险费（销售费用）": 1952675507458175007,
    "保险费-房屋保险费（销售费用）": 1952675507458175008,
    "保险费-存货保险费（销售费用）": 1952675507458175009,
    "保险费-货物运输保险费（销售费用）": 1952675507458175010,
    "保险费-其他（销售费用）": 1952675507458175011,
    "车杂费（销售费用）": 1952675507458175012,
    "燃料费（销售费用）": 1952675507458175013,
    "燃料费-车辆燃料费（销售费用）": 1952675507458175014,
    "折旧费（销售费用）": 1952675507458175015,
    "修理费（销售费用）": 1952675507458175016,
    "修理费-车辆修理费（销售费用）": 1952675507458175017,
    "国内市场营销费（销售费用）": 1952675507458175018,
    "国内市场营销费-媒介费（销售费用）": 1952675507458175019,
    "国内市场营销费-物料费（销售费用）": 1952675507458175020,
    "国际市场营销费（销售费用）": 1952675507458175021,
    "国际市场营销费-物料费（销售费用）": 1952675507458175022,
    "国际市场营销费-渠道建设费（销售费用）": 1952675507458175023,
    "国际市场营销费-其他（销售费用）": 1952675507458175024,
    "技术使用费（销售费用）": 1952675507458175025,
    "手续费（销售费用）": 1952675507458175026,
    "劳务费（销售费用）": 1952675507458175027,
    "商品损耗（销售费用）": 1952675507458175028,
    "烟叶临时人员工资（销售费用）": 1952675507458175029,
    "差旅费（销售费用）": 1952675507458175030,
    "海关费（销售费用）": 1952675507458175031,
    "熏蒸费（销售费用）": 1952675507458175032,
    "寄样费（销售费用）": 1952675507458175033,
    "滞报滞港费（销售费用）": 1952675507458175034,
    "码头费（销售费用）": 1952675507458175035,
    "交易手续费（销售费用）": 1952675507458175036,
    "低值易耗品摊销（销售费用）": 1952675507458175037,
    "零售终端建设费（销售费用）": 1952675507458175038,
    "水电费（销售费用）": 1952675507458175039,
    "长期待摊费用摊销（销售费用）": 1952675507458175040,
    "无形资产摊销（销售费用）": 1952675507458175041,
    "其他（销售费用）": 1952675507458175042,
    # 管理费用相关指标ID
    "职工薪酬（管理费用）": 1952675507458175043,
    "劳动保护费（管理费用）": 1952675507458175062,
    "车杂费（管理费用）": 1952675507458175063,
    "燃料费（管理费用）": 1952675507458175064,
    "折旧费（管理费用）": 1952675507458175066,
    "修理费（管理费用）": 1952675507458175067,
    "租赁费（管理费用）": 1952675507458175069,
    "通讯费（管理费用）": 1952675507458175070,
    "差旅费（管理费用）": 1952675507458175071,
    "会议费（管理费用）": 1952675507458175072,
    "业务招待费（管理费用）": 1952675507458175073,
    "企业文化建设费（管理费用）": 1952675507458175074,
    "办公费（管理费用）": 1952675507458175077,
    "水电费（管理费用）": 1952675507458175078,
    "物业管理费（管理费用）": 1952675507458175079,
    "书报费（管理费用）": 1952675507458175080,
    "保险费（管理费用）": 1952675507458175081,
    "企业研发费用（管理费用）": 1952675507458175087,
    "涉外费（管理费用）": 1952675507458175088,
    "诉讼费（管理费用）": 1952675507458175093,
    "中介费（管理费用）": 1952675507458175094,
    "打假经费（管理费用）": 1952675507458175095,
    "专卖管理经费（管理费用）": 1952675507458175096,
    "低值易耗品摊销（管理费用）": 1952675507458175097,
    "长期待摊费用摊销（管理费用）": 1952675507458175098,
    "无形资产摊销（管理费用）": 1952675507458175099,
    "存货盘盈盘亏（管理费用）": 1952675507458175100,
    "董事会费（管理费用）": 1952675507458175101,
    "排污费（管理费用）": 1952675507458175102,
    "绿化费（管理费用）": 1952675507458175103,
    "信息系统维护费（管理费用）": 1952675507458175104,
    "网络通讯费（管理费用）": 1952675507458175105,
    "警卫消防费（管理费用）": 1952675507458175106,
    "政府性基金（管理费用）": 1952675507458175107,
    "劳务费用（管理费用）": 1952675507458175114,
    "打私经费（管理费用）": 1952675507458175115,
    "协会会费（管理费用）": 1952675507458175122,
    "党组织工作经费（管理费用）": 1952675507458175123,
    "文明吸烟环境建设费（管理费用）": 1952675507458175124,
    "其他（管理费用）": 1952675507458175125,
    # 收入成本相关
    "营业收入": 1952675507478275179,
    "主营业务收入": 1952675507478275180,
    "其他业务收入": 1952675507478275181,
    "营业总成本": 1952675507478275186,
    "营业成本": 1952675507478275187,
    "主营业务成本": 1952675507478275188,
    "其他业务成本": 1952675507478275189
}

def main():
    print("开始生成预算数据INSERT语句...")

    # 使用预算项目映射
    project_mapping = budget_project_mapping
    
    # 基于预算.csv的实际数据（手工提取并转换）
    budget_data = [
        # 格式: (项目名称, 上半年执行数, 全年预算数, 全年调整预算数)
        ("一、营业总收入", 173695.19, 320646.36, 320655.58),
        ("其中：主营业务收入", 173661.49, 320576.36, 320588.58),
        ("其他业务收入", 33.70, 70.00, 67.00),
        ("二、营业总成本", 120814.00, 223849.31, 223856.86),
        ("其中：主营业务成本", 120716.14, 223653.31, 223660.86),
        ("其他业务成本", 97.86, 196.00, 196.00),
        ("税金及附加", 23649.77, 43673.00, 43690.00),
        ("其中：主营业务税金及附加", 23649.77, 43673.00, 43690.00),
        ("销售费用", 2561.13, 5227.24, 5765.50),
        ("管理费用", 6399.18, 17389.02, 16855.24),
        ("财务费用", -342.49, -539.00, -560.00),
        ("资产减值损失", 0, 0, 0),
        ("其中：坏账减值损失", 0, 0, 0),
        ("信用减值损失", 0, 0, 0),
        ("加：公允价值变动收益（损失以\"-\"号填列）", 0, 0, 0),
        ("加：投资收益（损失以\"-\"号填列）", 0, 0, 0),
        ("其中：对联营企业投资收益", 0, 0, 0),
        ("加：资产处置收益", 0, 0, 0),
        ("加：其他收益", 11.31, 20.00, 12.00),
        ("三、营业利润（亏损以\"-\"号填列）", 20624.91, 31066.79, 31059.98),
        ("加：营业外收入", 0, 0, 0),
        ("其中：非流动资产处置利得", 0, 0, 0),
        ("减：营业外支出", 1.98, 60.00, 140.00),
        ("其中：非流动资产处置损失", 0, 50.00, 50.00),
        ("四、利润总额（亏损总额以\"-\"号填列）", 20622.93, 31006.79, 30919.98),
        ("减：所得税费用", 5007.79, 7748.26, 7730.00),
        ("五、净利润（净亏损以\"-\"号填列）", 15615.14, 23258.53, 23189.98),
        ("六、应交税费合计", 36117.18, 63579.45, 63648.00),
        ("其中：应交增值税", 7550.08, 12158.19, 12228.00),
        ("应交消费税", 20248.39, 37455.00, 37457.00),
        ("应交城建税", 0, 0, 0),
        ("应交教育税", 1921.07, 3473.00, 3481.00),
        ("应交地方教育费附加", 833.91, 1488.00, 1492.00),
        ("应交地方水利建设基金附加", 555.94, 992.00, 995.00),
        ("应交所得税", 5007.79, 7748.26, 7730.00),
        ("其他税费（所得税和地方水利建设基金附加）", 0, 265.00, 265.00),
        ("七、税利合计", 51732.32, 86837.98, 86837.98),
        ("八、主要经营指标汇总", 196237.48, 362251.29, 362265.10),
        ("九、主要经营指标(收支)", 231944.20, 446249.99, 446249.76),
        ("十、坏账准备增加(减)", 0, 0, 0),
        # 重点指标
        ("管理费用率(%)", 4.96, 6.89, 6.88),
        ("成本费用利润率(%)", 15.95, 12.62, 12.58),
        # 财务指标
        ("销售毛利率(%)", 30.49, 30.23, 30.23),
        ("销售不含税单价(元/件)", 37436.05, 35918.92, 35920.31),
        ("销售含税单价(元/件)", 42302.74, 40588.38, 40589.95),
        ("销售税费(元/件)", 6706.22, 6255.60, 6265.33),
        ("销售成本(元/件)", 4445.67, 3474.15, 3464.43),
        ("销售税利(元/件)", 11151.89, 9729.75, 9729.75),
        ("销售(管)费用(元/件)", 1857.74, 2473.64, 2471.79),
        ("销售(管)毛利(元/件)", 11413.38, 10859.73, 10860.25)
    ]
    
    insert_statements = []
    
    for project_name, value1, value2, value3 in budget_data:
        # 查找对应的指标名称
        indicator_name = project_mapping.get(project_name)
        if not indicator_name:
            # 尝试直接匹配
            indicator_name = project_name
            
        # 查找指标ID
        indicator_id = indicator_id_mapping.get(indicator_name)
        if not indicator_id:
            print(f"警告: 项目 '{project_name}' -> 指标 '{indicator_name}' 未找到对应的ID")
            continue
            
        # 生成INSERT语句
        # 假设表结构: indicator_id, year, period_type, budget_amount, create_time
        statements = [
            f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, period_type, budget_amount, create_time) VALUES ({indicator_id}, 2025, '上半年执行', {value1}, NOW());",
            f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, period_type, budget_amount, create_time) VALUES ({indicator_id}, 2025, '全年预算', {value2}, NOW());",
            f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, period_type, budget_amount, create_time) VALUES ({indicator_id}, 2025, '全年调整预算', {value3}, NOW());"
        ]
        insert_statements.extend(statements)
    
    # 输出结果
    print(f"生成了 {len(insert_statements)} 条INSERT语句")
    
    # 保存到文件
    with open("budget_insert_statements.sql", "w", encoding="utf-8") as f:
        f.write("-- 预算数据INSERT语句\n")
        f.write("-- 注意：需要根据实际的yjzb_indicator_annual_budget表结构调整字段\n\n")
        for stmt in insert_statements:
            f.write(stmt + "\n")
    
    print("INSERT语句已保存到 budget_insert_statements.sql")
    
    # 显示示例
    print("\n生成的INSERT语句示例:")
    for i, stmt in enumerate(insert_statements[:6]):
        print(f"{i+1}. {stmt}")

if __name__ == "__main__":
    main()
