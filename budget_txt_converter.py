#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
预算.txt转换为INSERT INTO yjzb_indicator_annual_budget语句
字段映射：
- 年初预算数 → initial_budget
- 中期调整申报数 → midyear_budget  
- 上半年执行数 → initial_reported
"""

import re
import csv

# 预算项目名称到指标名称的映射
project_to_indicator_mapping = {
    # 收入类
    "一、营业收入": "营业总收入",
    "其中：主营业务收入": "主营业务收入",
    "其他业务收入": "其他业务收入",
    
    # 成本费用类
    "二、营业成本": "营业总成本",
    "其中：主营业务成本": "主营业务成本",
    "其他业务成本": "其他业务成本",
    "税金及附加": "税金及附加",
    "其中：主营业务税金及附加": "主营业务税金及附加",
    "销售费用": "销售费用",
    "管理费用": "管理费用",
    "财务费用": "财务费用",
    "资产减值损失": "资产减值损失",
    "其中：烟叶减值损失": "烟叶减值损失",
    "信用减值损失": "信用减值损失",
    
    # 投资收益类
    "加：公允价值变动收益（损失以\"-\"号填列）": "公允价值变动收益",
    "加：投资收益（损失以\"-\"号填列）": "投资收益",
    "其中：主业投资收益": "主业投资收益",
    "加：资产处置收益": "资产处置收益",
    "加：其他收益": "其他收益",
    
    # 利润类
    "三、营业利润（亏损以\"－\"号填列）": "营业利润",
    "加：营业外收入": "营业外收入",
    "其中：补贴收入": "补贴收入",
    "减：营业外支出": "营业外支出",
    "其中：捐赠支出": "捐赠支出",
    "四、利润总额（亏损总额以\"－\"号填列）": "利润总额",
    "减：所得税费用": "所得税费用",
    "五、净利润（净亏损以\"－\"号填列）": "净利润",
    
    # 税费类
    "六、应交税金合计": "应交税金合计",
    "其中：应交增值税": "应交增值税",
    "应交消费税": "应交消费税",
    "应交烟叶税": "应交烟叶税",
    "应交城建税": "应交城建税",
    "应交教育费附加": "应交教育费附加",
    "应交地方教育费附加": "应交地方教育费附加",
    "应交所得税": "应交所得税",
    "其他各税（不含地方教育费附加）": "其他各税",
    
    # 综合指标
    "七、税利合计": "税利合计",
    "八、商业卷烟销售额": "商业卷烟销售额",
    "九、卷烟销售数量(万支)": "卷烟销售数量",
    "十、烟叶销售数量(万担)": "烟叶销售数量",
    
    # 财务比率指标
    "三项费用率(%)": "三项费用率",
    "成本费用利润率(%)": "成本费用利润率",
    "销售毛利率(%)": "销售毛利率",
    "单箱不含税收入（元/箱）": "单箱不含税收入",
    "单箱含税收入（元/箱）": "单箱含税收入",
    "单箱税金（元/箱）": "单箱税金",
    "单箱利润（元/箱）": "单箱利润",
    "单箱税利（元/箱）": "单箱税利",
    "单箱(万担)费用（元/箱）": "单箱费用",
    "单箱(万担)毛利额（元/箱）": "单箱毛利额"
}

# 指标名称到ID的映射（基于yjzb_indicator.sql）
indicator_name_to_id = {
    # 收入类指标
    "营业总收入": 1952675507478275178,
    "营业收入": 1952675507478275179,
    "主营业务收入": 1952675507478275180,
    "其他业务收入": 1952675507478275181,
    
    # 成本费用类指标
    "营业总成本": 1952675507478275186,
    "营业成本": 1952675507478275187,
    "主营业务成本": 1952675507478275188,
    "其他业务成本": 1952675507478275189,
    "税金及附加": 1952675507478275002,
    "销售费用": 1952675507478275003,
    "管理费用": 1952675507478275004,
    "财务费用": 1952675507478275006,
    
    # 利润类指标
    "其他收益": 1952675507478275011,
    "营业利润": 1952675507478275021,
    "营业外收入": 1952675507478275022,
    "营业外支出": 1952675507478275024,
    "利润总额": 1952675507478275025,
    "所得税费用": 1952675507478275026,
    "净利润": 1952675507478275027,
    "税利合计": 1952675507478275216,
    
    # 投资收益类
    "投资收益": 1952675507478275012,
    "资产处置收益": 1952675507478275020,
    "公允价值变动收益": 1952675507478275014,
    
    # 应交税费类（使用现有的相关指标ID）
    "应交税金合计": 1952675507478275212,
    "应交企业所得税": 1952675507478275211,
    "应交所得税": 1952675507478275215
}

def clean_amount(amount_str):
    """清理金额字符串，转换为数字"""
    if not amount_str or amount_str.strip() == '-' or amount_str.strip() == '':
        return None
    
    # 移除空格、逗号等格式字符
    cleaned = str(amount_str).replace(' ', '').replace(',', '').replace('"', '').strip()
    
    if cleaned == '-' or cleaned == '':
        return None
        
    try:
        return float(cleaned)
    except ValueError:
        return None

def parse_budget_txt(file_path):
    """解析预算.txt文件"""
    budget_data = []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines[3:]:  # 跳过前3行标题
        if line.strip():
            parts = line.strip().split(',')
            if len(parts) >= 5:
                project_name = parts[0].strip()
                if project_name and not project_name.isdigit():
                    initial_reported = clean_amount(parts[2])  # 上半年执行数
                    initial_budget = clean_amount(parts[3])    # 年初预算数
                    midyear_budget = clean_amount(parts[4])    # 中期调整申报数
                    
                    budget_data.append({
                        'project_name': project_name,
                        'initial_reported': initial_reported,
                        'initial_budget': initial_budget,
                        'midyear_budget': midyear_budget
                    })
    
    return budget_data

def generate_insert_statements():
    """生成INSERT语句"""
    print("开始解析预算.txt文件...")
    
    # 解析预算文件
    budget_data = parse_budget_txt("预算.txt")
    print(f"解析到 {len(budget_data)} 个预算项目")
    
    insert_statements = []
    matched_count = 0
    unmatched_projects = []
    
    for item in budget_data:
        project_name = item['project_name']
        
        # 查找对应的指标名称
        indicator_name = project_to_indicator_mapping.get(project_name)
        if not indicator_name:
            indicator_name = project_name  # 尝试直接匹配
        
        # 查找指标ID
        indicator_id = indicator_name_to_id.get(indicator_name)
        
        if indicator_id:
            matched_count += 1
            
            # 生成INSERT语句
            if item['initial_reported'] is not None:
                insert_statements.append(
                    f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, initial_reported, create_time) "
                    f"VALUES ({indicator_id}, 2025, {item['initial_reported']}, NOW());"
                )
            
            if item['initial_budget'] is not None:
                insert_statements.append(
                    f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, initial_budget, create_time) "
                    f"VALUES ({indicator_id}, 2025, {item['initial_budget']}, NOW());"
                )
            
            if item['midyear_budget'] is not None:
                insert_statements.append(
                    f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, midyear_budget, create_time) "
                    f"VALUES ({indicator_id}, 2025, {item['midyear_budget']}, NOW());"
                )
        else:
            unmatched_projects.append(f"{project_name} -> {indicator_name}")
    
    print(f"成功匹配 {matched_count} 个项目")
    print(f"未匹配项目 {len(unmatched_projects)} 个")
    
    if unmatched_projects:
        print("\n未匹配的项目:")
        for project in unmatched_projects:
            print(f"  - {project}")
    
    # 保存INSERT语句
    output_file = "budget_insert_statements_new.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 预算数据INSERT语句\n")
        f.write("-- 基于预算.txt文件生成\n")
        f.write("-- 字段映射：年初预算数→initial_budget, 中期调整申报数→midyear_budget, 上半年执行数→initial_reported\n\n")
        
        for stmt in insert_statements:
            f.write(stmt + "\n")
    
    print(f"\n生成了 {len(insert_statements)} 条INSERT语句")
    print(f"INSERT语句已保存到 {output_file}")
    
    # 显示前几条语句作为示例
    print("\n前10条INSERT语句示例:")
    for i, stmt in enumerate(insert_statements[:10]):
        print(f"{i+1}. {stmt}")

if __name__ == "__main__":
    generate_insert_statements()
