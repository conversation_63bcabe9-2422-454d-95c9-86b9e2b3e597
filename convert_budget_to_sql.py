#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将预算.csv转换为INSERT INTO yjzb_indicator_annual_budget语句
"""

import csv
import re
import pandas as pd
from typing import Dict, List, Tuple

def read_csv_with_encoding(file_path: str, encodings: List[str] = ['utf-8', 'gbk', 'gb2312', 'utf-8-sig']) -> pd.DataFrame:
    """尝试不同编码读取CSV文件"""
    for encoding in encodings:
        try:
            return pd.read_csv(file_path, encoding=encoding)
        except UnicodeDecodeError:
            continue
        except Exception as e:
            print(f"Error reading with {encoding}: {e}")
            continue
    raise Exception(f"无法读取文件 {file_path}")

def extract_indicators_from_template(file_path: str) -> Dict[str, str]:
    """从模板文件中提取指标名称映射"""
    indicators = {}
    try:
        df = read_csv_with_encoding(file_path)
        for _, row in df.iterrows():
            for col in df.columns:
                cell_value = str(row[col])
                # 查找 {指标名称} 格式的内容
                matches = re.findall(r'\{([^}]+)\}', cell_value)
                for match in matches:
                    # 使用项目名称作为key，指标名称作为value
                    project_name = str(row.iloc[0]) if not pd.isna(row.iloc[0]) else ""
                    if project_name and project_name != 'nan':
                        indicators[project_name.strip()] = match.strip()
    except Exception as e:
        print(f"读取模板文件 {file_path} 时出错: {e}")
    return indicators

def parse_indicator_sql(file_path: str) -> Dict[str, int]:
    """解析指标SQL文件，获取指标名称到ID的映射"""
    indicator_map = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 查找INSERT语句中的指标信息
        pattern = r"INSERT INTO.*?VALUES\s*\((\d+),\s*'([^']+)'"
        matches = re.findall(pattern, content, re.DOTALL)
        
        for match in matches:
            indicator_id = int(match[0])
            indicator_name = match[1]
            indicator_map[indicator_name] = indicator_id
            
    except Exception as e:
        print(f"解析指标SQL文件时出错: {e}")
    
    return indicator_map

def main():
    # 文件路径
    budget_file = "预算.csv"
    template1_file = "三项费用数据模板.csv"
    template2_file = "税利指标明细表数据模板.csv"
    indicator_sql_file = "yjzb_indicator.sql"
    
    print("开始处理文件...")
    
    # 1. 读取模板文件，提取指标映射
    print("读取三项费用数据模板...")
    template1_indicators = extract_indicators_from_template(template1_file)
    print(f"从三项费用模板提取到 {len(template1_indicators)} 个指标映射")
    
    print("读取税利指标明细表模板...")
    template2_indicators = extract_indicators_from_template(template2_file)
    print(f"从税利指标模板提取到 {len(template2_indicators)} 个指标映射")
    
    # 合并指标映射
    all_indicators = {**template1_indicators, **template2_indicators}
    print(f"总共提取到 {len(all_indicators)} 个指标映射")
    
    # 2. 解析指标SQL文件
    print("解析指标SQL文件...")
    indicator_id_map = parse_indicator_sql(indicator_sql_file)
    print(f"从SQL文件解析到 {len(indicator_id_map)} 个指标ID映射")
    
    # 3. 读取预算文件
    print("读取预算文件...")
    try:
        budget_df = read_csv_with_encoding(budget_file)
        print(f"预算文件包含 {len(budget_df)} 行数据")
        print("预算文件列名:", budget_df.columns.tolist())
        
        # 显示前几行数据以便调试
        print("\n预算文件前5行:")
        print(budget_df.head())
        
    except Exception as e:
        print(f"读取预算文件失败: {e}")
        return
    
    # 4. 生成INSERT语句
    print("\n生成INSERT语句...")
    insert_statements = []
    
    # 假设预算文件的第一列是项目名称，后面的列是不同期间的预算数据
    for index, row in budget_df.iterrows():
        project_name = str(row.iloc[0]).strip() if not pd.isna(row.iloc[0]) else ""
        
        if not project_name or project_name == 'nan':
            continue
            
        # 查找对应的指标名称
        indicator_name = all_indicators.get(project_name)
        if not indicator_name:
            print(f"警告: 项目 '{project_name}' 未找到对应的指标名称")
            continue
            
        # 查找指标ID
        indicator_id = indicator_id_map.get(indicator_name)
        if not indicator_id:
            print(f"警告: 指标 '{indicator_name}' 未找到对应的ID")
            continue
            
        # 生成INSERT语句（这里需要根据实际的表结构调整）
        # 假设表结构包含: indicator_id, year, period, budget_amount等字段
        for col_index in range(1, len(row)):
            if not pd.isna(row.iloc[col_index]):
                budget_value = str(row.iloc[col_index]).replace(',', '').replace(' ', '')
                if budget_value and budget_value != '-':
                    try:
                        budget_amount = float(budget_value)
                        # 这里需要根据实际需求调整INSERT语句的字段
                        insert_sql = f"INSERT INTO yjzb_indicator_annual_budget (indicator_id, year, period, budget_amount) VALUES ({indicator_id}, 2025, {col_index}, {budget_amount});"
                        insert_statements.append(insert_sql)
                    except ValueError:
                        print(f"警告: 无法转换预算金额 '{budget_value}' 为数字")
    
    # 5. 输出结果
    print(f"\n生成了 {len(insert_statements)} 条INSERT语句")
    
    # 保存到文件
    output_file = "budget_insert_statements.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 预算数据INSERT语句\n")
        f.write("-- 生成时间: " + str(pd.Timestamp.now()) + "\n\n")
        for stmt in insert_statements:
            f.write(stmt + "\n")
    
    print(f"INSERT语句已保存到 {output_file}")
    
    # 显示前几条语句作为示例
    print("\n前10条INSERT语句示例:")
    for i, stmt in enumerate(insert_statements[:10]):
        print(f"{i+1}. {stmt}")

if __name__ == "__main__":
    main()
