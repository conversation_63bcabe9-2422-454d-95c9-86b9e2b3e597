#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成完整格式的INSERT INTO yjzb_indicator_annual_budget语句
"""

# 预算数据（从预算.txt提取）
budget_data = [
    # (项目名称, 上半年执行数, 年初预算数, 中期调整申报数)
    ("一、营业收入", 173695.19, 320646.36, 320655.58),
    ("其中：主营业务收入", 173661.49, 320576.36, 320588.58),
    ("其他业务收入", 33.70, 70.00, 67.00),
    ("二、营业成本", 120814.00, 223849.31, 223856.86),
    ("其中：主营业务成本", 120716.14, 223653.31, 223660.86),
    ("其他业务成本", 97.86, 196.00, 196.00),
    ("税金及附加", 23649.77, 43673.00, 43690.00),
    ("其中：主营业务税金及附加", 23649.77, 43673.00, 43690.00),
    ("销售费用", 2561.13, 5227.24, 5765.50),
    ("管理费用", 6399.18, 17389.02, 16855.24),
    ("财务费用", -342.49, -539.00, -560.00),
    ("资产减值损失", 0, 0, 0),
    ("其中：烟叶减值损失", 0, 0, 0),
    ("信用减值损失", 0, 0, 0),
    ("加：公允价值变动收益（损失以\"-\"号填列）", 0, 0, 0),
    ("加：投资收益（损失以\"-\"号填列）", 0, 0, 0),
    ("其中：主业投资收益", 0, 0, 0),
    ("加：资产处置收益", 0, 0, 0),
    ("加：其他收益", 11.31, 20.00, 12.00),
    ("三、营业利润（亏损以\"－\"号填列）", 20624.91, 31066.79, 31059.98),
    ("加：营业外收入", 0, 0, 0),
    ("其中：补贴收入", 0, 0, 0),
    ("减：营业外支出", 1.98, 60.00, 140.00),
    ("其中：捐赠支出", 0, 50.00, 50.00),
    ("四、利润总额（亏损总额以\"－\"号填列）", 20622.93, 31006.79, 30919.98),
    ("减：所得税费用", 5007.79, 7748.26, 7730.00),
    ("五、净利润（净亏损以\"－\"号填列）", 15615.14, 23258.53, 23189.98),
    ("六、应交税金合计", 36117.18, 63579.45, 63648.00),
    ("其中：应交增值税", 7550.08, 12158.19, 12228.00),
    ("应交消费税", 20248.39, 37455.00, 37457.00),
    ("应交烟叶税", 0, 0, 0),
    ("应交城建税", 1921.07, 3473.00, 3481.00),
    ("应交教育费附加", 833.91, 1488.00, 1492.00),
    ("应交地方教育费附加", 555.94, 992.00, 995.00),
    ("应交所得税", 5007.79, 7748.26, 7730.00),
    ("其他各税（不含地方教育费附加）", 0, 265.00, 265.00),
    ("七、税利合计", 51732.32, 86837.98, 86837.98),
    ("八、商业卷烟销售额", 196237.48, 362251.29, 362265.10),
    ("九、卷烟销售数量(万支)", 231944.20, 446249.99, 446249.76),
    ("十、烟叶销售数量(万担)", 0, 0, 0),
    ("三项费用率(%)", 4.96, 6.89, 6.88),
    ("成本费用利润率(%)", 15.95, 12.62, 12.58),
    ("销售毛利率(%)", 30.49, 30.23, 30.23),
    ("单箱不含税收入（元/箱）", 37436.05, 35918.92, 35920.31),
    ("单箱含税收入（元/箱）", 42302.74, 40588.38, 40589.95),
    ("单箱税金（元/箱）", 6706.22, 6255.60, 6265.33),
    ("单箱利润（元/箱）", 4445.67, 3474.15, 3464.43),
    ("单箱税利（元/箱）", 11151.89, 9729.75, 9729.75),
    ("单箱(万担)费用（元/箱）", 1857.74, 2473.64, 2471.79),
    ("单箱(万担)毛利额（元/箱）", 11413.38, 10859.73, 10860.25)
]

# 项目名称到指标名称的映射（包含预算.txt中的所有项目）
project_to_indicator = {
    # 收入类
    "一、营业收入": "营业总收入",
    "其中：主营业务收入": "主营业务收入",
    "其他业务收入": "其他业务收入",

    # 成本费用类
    "二、营业成本": "营业总成本",
    "其中：主营业务成本": "主营业务成本",
    "其他业务成本": "其他业务成本",
    "税金及附加": "税金及附加",
    "其中：主营业务税金及附加": "主营业务税金及附加",
    "销售费用": "销售费用",
    "管理费用": "管理费用",
    "财务费用": "财务费用",
    "资产减值损失": "资产减值损失",
    "其中：烟叶减值损失": "烟叶减值损失",
    "信用减值损失": "信用减值损失",

    # 投资收益类
    "加：公允价值变动收益（损失以\"-\"号填列）": "公允价值变动收益",
    "加：投资收益（损失以\"-\"号填列）": "投资收益",
    "其中：主业投资收益": "主业投资收益",
    "加：资产处置收益": "资产处置收益",
    "加：其他收益": "其他收益",

    # 利润类
    "三、营业利润（亏损以\"－\"号填列）": "营业利润",
    "加：营业外收入": "营业外收入",
    "其中：补贴收入": "补贴收入",
    "减：营业外支出": "营业外支出",
    "其中：捐赠支出": "捐赠支出",
    "四、利润总额（亏损总额以\"－\"号填列）": "利润总额",
    "减：所得税费用": "所得税费用",
    "五、净利润（净亏损以\"－\"号填列）": "净利润",

    # 税费类
    "六、应交税金合计": "应交税金合计",
    "其中：应交增值税": "应交增值税",
    "应交消费税": "应交消费税",
    "应交烟叶税": "应交烟叶税",
    "应交城建税": "应交城建税",
    "应交教育费附加": "应交教育费附加",
    "应交地方教育费附加": "应交地方教育费附加",
    "应交所得税": "应交所得税",
    "其他各税（不含地方教育费附加）": "其他各税",

    # 综合指标
    "七、税利合计": "税利合计",
    "八、商业卷烟销售额": "商业卷烟销售额",
    "九、卷烟销售数量(万支)": "卷烟销售数量",
    "十、烟叶销售数量(万担)": "烟叶销售数量",

    # 财务比率指标
    "三项费用率(%)": "三项费用率",
    "成本费用利润率(%)": "成本费用利润率",
    "销售毛利率(%)": "销售毛利率",
    "单箱不含税收入（元/箱）": "单箱不含税收入",
    "单箱含税收入（元/箱）": "单箱含税收入",
    "单箱税金（元/箱）": "单箱税金",
    "单箱利润（元/箱）": "单箱利润",
    "单箱税利（元/箱）": "单箱税利",
    "单箱(万担)费用（元/箱）": "单箱费用",
    "单箱(万担)毛利额（元/箱）": "单箱毛利额"
}

# 指标名称到ID的映射
indicator_to_id = {
    "营业总收入": 1952675507478275178,
    "主营业务收入": 1952675507478275180,
    "其他业务收入": 1952675507478275181,
    "营业总成本": 1952675507478275186,
    "主营业务成本": 1952675507478275188,
    "其他业务成本": 1952675507478275189,
    "税金及附加": 1952675507478275002,
    "销售费用": 1952675507478275003,
    "管理费用": 1952675507478275004,
    "财务费用": 1952675507478275006,
    "其他收益": 1952675507478275011,
    "营业利润": 1952675507478275021,
    "营业外支出": 1952675507478275024,
    "利润总额": 1952675507478275025,
    "所得税费用": 1952675507478275026,
    "净利润": 1952675507478275027,
    "应交税金合计": 1952675507478275212,
    "应交所得税": 1952675507478275215,
    "税利合计": 1952675507478275216
}

def generate_complete_insert_statements():
    """生成完整格式的INSERT语句"""
    insert_statements = []
    start_id = 401  # 起始ID
    user_id = 1123598821738675201  # 用户ID
    timestamp = '2025-01-27 10:00:00'  # 时间戳
    
    current_id = start_id
    
    for project_name, initial_reported, initial_budget, midyear_budget in budget_data:
        # 查找指标名称
        indicator_name = project_to_indicator.get(project_name, project_name)
        
        # 查找指标ID
        indicator_id = indicator_to_id.get(indicator_name)
        
        if indicator_id:
            # 生成完整的INSERT语句
            insert_sql = (
                f'INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES '
                f'({current_id}, {indicator_id}, \'{indicator_name}\', 2025, '
                f'{initial_reported:.6f}, {initial_budget:.6f}, {midyear_budget:.6f}, '
                f'NULL, NULL, {user_id}, NULL, \'{timestamp}\', {user_id}, \'{timestamp}\', 1, 0);'
            )
            insert_statements.append(insert_sql)
            current_id += 1
        else:
            print(f"未匹配指标: {project_name} -> {indicator_name}")
    
    return insert_statements

def main():
    print("生成完整格式的INSERT语句...")
    
    statements = generate_complete_insert_statements()
    
    # 保存到文件
    output_file = "budget_complete_insert_statements.sql"
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write("-- 预算数据完整INSERT语句\n")
        f.write("-- 基于预算.txt文件生成\n")
        f.write("-- 表结构：id, indicator_id, indicator_name, year, initial_reported, initial_budget, midyear_budget, field4, field5, create_by, update_by, create_time, user_id, update_time, status, deleted\n\n")
        
        for stmt in statements:
            f.write(stmt + "\n")
    
    print(f"生成了 {len(statements)} 条完整INSERT语句")
    print(f"INSERT语句已保存到 {output_file}")
    
    # 显示前3条语句作为示例
    print("\n前3条INSERT语句示例:")
    for i, stmt in enumerate(statements[:3]):
        print(f"{i+1}. {stmt}")

if __name__ == "__main__":
    main()
