/*
 Navicat Premium Dump SQL

 Source Server         : gzyc-nlp-2024.rwlb.rds.aliyuncs.com
 Source Server Type    : PostgreSQL
 Source Server Version : 140011 (140011)
 Source Host           : gzyc-nlp-2024.rwlb.rds.aliyuncs.com:1921
 Source Catalog        : yjyc
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 140011 (140011)
 File Encoding         : 65001

 Date: 02/09/2025 11:02:55
*/


-- ----------------------------
-- Table structure for yjzb_indicator_annual_budget
-- ----------------------------
DROP TABLE IF EXISTS "public"."yjzb_indicator_annual_budget";
CREATE TABLE "public"."yjzb_indicator_annual_budget" (
  "id" int8 NOT NULL DEFAULT nextval('yjzb_indicator_annual_budget_id_seq'::regclass),
  "indicator_id" int8 NOT NULL,
  "indicator_name" varchar(100) COLLATE "pg_catalog"."default" NOT NULL,
  "year" int4 NOT NULL,
  "initial_budget" numeric(18,6) DEFAULT 0,
  "initial_reported" numeric(18,6) DEFAULT 0,
  "midyear_budget" numeric(18,6) DEFAULT 0,
  "midyear_reported" numeric(18,6) DEFAULT 0,
  "current_used" numeric(18,6) DEFAULT 0,
  "create_user" int8,
  "create_dept" int8,
  "create_time" timestamp(6) DEFAULT now(),
  "update_user" int8,
  "update_time" timestamp(6),
  "status" int4 DEFAULT 1,
  "is_deleted" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."id" IS '主键ID';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."indicator_id" IS '指标ID（关联指标表）';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."indicator_name" IS '指标名称（为历史保留冗余）';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."year" IS '年份（YYYY）';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."initial_budget" IS '年初预算数';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."initial_reported" IS '年初报备数';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."midyear_budget" IS '中期调整预算数';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."midyear_reported" IS '中期调整报备数';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."current_used" IS '当前使用数';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."create_user" IS '创建人ID';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."create_dept" IS '创建部门ID';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."create_time" IS '创建时间';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."update_user" IS '更新人ID';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."update_time" IS '更新时间';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."status" IS '数据状态（1-正常，0-禁用）';
COMMENT ON COLUMN "public"."yjzb_indicator_annual_budget"."is_deleted" IS '删除标记（0-未删除，1-已删除）';
COMMENT ON TABLE "public"."yjzb_indicator_annual_budget" IS '指标年度预算表 - 存储每年预算、报备及当前使用情况';

-- ----------------------------
-- Records of yjzb_indicator_annual_budget
-- ----------------------------
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (401, 1952675507458175077, '办公费（管理费用）', 2025, 61255.750000, 63483.530000, 73715.360000, 79920.130000, 1920000.000000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (2, 1952675507458174978, '办公费（销售费用）', 2022, 79471.440000, 77449.660000, 79865.930000, 77900.780000, 63322.830000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (3, 1952675507458174978, '办公费（销售费用）', 2023, 64522.650000, 76483.790000, 75976.510000, 64281.300000, 77623.050000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (6, 1952675507458174979, '业务招待费（销售费用）', 2022, 76213.400000, 66479.670000, 65907.820000, 72193.820000, 62945.850000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (7, 1952675507458174979, '业务招待费（销售费用）', 2023, 75702.000000, 61797.370000, 72666.060000, 76796.050000, 60571.580000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (8, 1952675507458174979, '业务招待费（销售费用）', 2024, 79845.600000, 72745.670000, 66005.490000, 62798.900000, 75192.800000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (9, 1952675507458174979, '业务招待费（销售费用）', 2025, 69435.870000, 74805.790000, 66459.040000, 69317.630000, 62741.390000, 1123598821738675201, NULL, '2025-08-08 15:41:51', 1123598821738675201, '2025-08-08 15:41:51', 1, 0);