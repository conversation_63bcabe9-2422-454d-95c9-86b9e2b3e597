# 预算.txt转换为INSERT语句 - 最终结果

## 转换完成情况

✅ **成功生成了19条完整的INSERT语句**

### 📊 字段映射关系
- **上半年执行数** → `initial_reported`
- **年初预算数** → `initial_budget`  
- **中期调整申报数** → `midyear_budget`

### 🎯 INSERT语句格式
```sql
INSERT INTO "public"."yjzb_indicator_annual_budget" VALUES (
    id,                    -- 主键ID (从401开始递增)
    indicator_id,          -- 指标ID (从yjzb_indicator.sql获取)
    'indicator_name',      -- 指标名称
    2025,                  -- 年份
    initial_reported,      -- 上半年执行数
    initial_budget,        -- 年初预算数
    midyear_budget,        -- 中期调整申报数
    NULL,                  -- 第4个数值字段
    NULL,                  -- 第5个数值字段
    1123598821738675201,   -- create_by (创建人)
    NULL,                  -- update_by (更新人)
    '2025-01-27 10:00:00', -- create_time (创建时间)
    1123598821738675201,   -- 用户ID字段
    '2025-01-27 10:00:00', -- update_time (更新时间)
    1,                     -- status (状态)
    0                      -- deleted (删除标记)
);
```

### ✅ 成功匹配的19个指标

| ID | 指标ID | 指标名称 | 上半年执行数 | 年初预算数 | 中期调整申报数 |
|---|---|---|---|---|---|
| 401 | 1952675507478275178 | 营业总收入 | 173695.19 | 320646.36 | 320655.58 |
| 402 | 1952675507478275180 | 主营业务收入 | 173661.49 | 320576.36 | 320588.58 |
| 403 | 1952675507478275181 | 其他业务收入 | 33.70 | 70.00 | 67.00 |
| 404 | 1952675507478275186 | 营业总成本 | 120814.00 | 223849.31 | 223856.86 |
| 405 | 1952675507478275188 | 主营业务成本 | 120716.14 | 223653.31 | 223660.86 |
| 406 | 1952675507478275189 | 其他业务成本 | 97.86 | 196.00 | 196.00 |
| 407 | 1952675507478275002 | 税金及附加 | 23649.77 | 43673.00 | 43690.00 |
| 408 | 1952675507478275003 | 销售费用 | 2561.13 | 5227.24 | 5765.50 |
| 409 | 1952675507478275004 | 管理费用 | 6399.18 | 17389.02 | 16855.24 |
| 410 | 1952675507478275006 | 财务费用 | -342.49 | -539.00 | -560.00 |
| 411 | 1952675507478275011 | 其他收益 | 11.31 | 20.00 | 12.00 |
| 412 | 1952675507478275021 | 营业利润 | 20624.91 | 31066.79 | 31059.98 |
| 413 | 1952675507478275024 | 营业外支出 | 1.98 | 60.00 | 140.00 |
| 414 | 1952675507478275025 | 利润总额 | 20622.93 | 31006.79 | 30919.98 |
| 415 | 1952675507478275026 | 所得税费用 | 5007.79 | 7748.26 | 7730.00 |
| 416 | 1952675507478275027 | 净利润 | 15615.14 | 23258.53 | 23189.98 |
| 417 | 1952675507478275212 | 应交税金合计 | 36117.18 | 63579.45 | 63648.00 |
| 418 | 1952675507478275215 | 应交所得税 | 5007.79 | 7748.26 | 7730.00 |
| 419 | 1952675507478275216 | 税利合计 | 51732.32 | 86837.98 | 86837.98 |

### ⚠️ 未匹配的指标（需要先在yjzb_indicator表中创建）

以下30个指标未找到对应的ID：
- 主营业务税金及附加
- 资产减值损失、烟叶减值损失、信用减值损失
- 公允价值变动收益、投资收益、主业投资收益、资产处置收益
- 营业外收入、补贴收入、捐赠支出
- 应交增值税、应交消费税、应交烟叶税、应交城建税等税费指标
- 商业卷烟销售额、卷烟销售数量、烟叶销售数量
- 各种财务比率指标（费用率、利润率、单箱指标等）

### 📁 生成的文件

1. **budget_complete_insert_statements.sql** - 最终的19条完整INSERT语句
2. **generate_complete_insert.py** - 生成脚本
3. **最终转换结果说明.md** - 本说明文档

### 🚀 使用方法

直接执行SQL文件：
```sql
-- 在数据库中执行
\i budget_complete_insert_statements.sql
```

或者复制粘贴单条语句执行。

### 📋 后续工作建议

1. **补全指标**：为未匹配的30个指标在yjzb_indicator表中创建记录
2. **调整ID**：根据实际数据库中的下一个可用ID调整起始ID（当前从401开始）
3. **验证数据**：执行后验证数据的准确性
4. **时间戳**：根据需要调整create_time和update_time
