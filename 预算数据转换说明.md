# 预算数据转换为INSERT语句说明文档

## 概述
本文档说明了如何将预算.csv文件转换为yjzb_indicator_annual_budget表的INSERT语句的完整过程。

## 文件说明

### 输入文件
1. **预算.csv** - 包含预算数据的主文件
2. **三项费用数据模板.csv** - 包含销售费用、管理费用、财务费用的指标映射模板
3. **税利指标明细表数据模板.csv** - 包含税费和利润相关指标的映射模板
4. **yjzb_indicator.sql** - 包含指标ID和名称映射关系的SQL文件

### 输出文件
1. **budget_insert_statements.sql** - 自动生成的部分INSERT语句
2. **complete_budget_insert_generator.sql** - 手工完善的完整INSERT语句
3. **budget_converter.py** - Python转换脚本
4. **convert_budget_to_sql.py** - 原始转换脚本（功能更全面但未能完全运行）

## 转换逻辑

### 1. 指标映射关系
转换过程基于以下映射关系：

**预算.csv项目名称** → **指标名称** → **指标ID**

例如：
- "一、营业总收入" → "营业总收入" → 1952675507478275178
- "其中：主营业务收入" → "主营业务收入" → 1952675507478275180
- "销售费用" → "销售费用" → 1952675507478275003

### 2. 数据结构
预算.csv文件包含以下列：
- 项目名称（第1列）
- 上半年执行数（第2列）
- 全年预算数（第3列）
- 全年调整预算数（第4列）

### 3. 转换结果
每个预算项目生成3条INSERT语句，分别对应：
- 上半年执行数据
- 全年预算数据
- 全年调整预算数据

## 成功转换的指标

以下指标已成功匹配并生成INSERT语句：

### 收入类指标
- 营业总收入 (ID: 1952675507478275178)
- 主营业务收入 (ID: 1952675507478275180)
- 其他业务收入 (ID: 1952675507478275181)

### 成本费用类指标
- 营业总成本 (ID: 1952675507478275186)
- 主营业务成本 (ID: 1952675507478275188)
- 其他业务成本 (ID: 1952675507478275189)
- 税金及附加 (ID: 1952675507478275002)
- 销售费用 (ID: 1952675507478275003)
- 管理费用 (ID: 1952675507478275004)
- 财务费用 (ID: 1952675507478275006)

### 利润类指标
- 其他收益 (ID: 1952675507478275011)
- 营业利润 (ID: 1952675507478275021)
- 营业外收入 (ID: 1952675507478275022)
- 营业外支出 (ID: 1952675507478275024)
- 利润总额 (ID: 1952675507478275025)
- 所得税费用 (ID: 1952675507478275026)
- 净利润 (ID: 1952675507478275027)
- 税利合计 (ID: 1952675507478275216)

## 未匹配的指标

以下指标在yjzb_indicator.sql中未找到对应的ID，需要进一步处理：

### 税费类指标
- 应交增值税
- 应交消费税
- 应交城建税
- 应交教育税
- 应交地方教育费附加
- 应交地方水利建设基金附加
- 应交所得税
- 其他税费

### 财务比率指标
- 管理费用率(%)
- 成本费用利润率(%)
- 销售毛利率(%)
- 销售不含税单价(元/件)
- 销售含税单价(元/件)
- 销售税费(元/件)
- 销售成本(元/件)
- 销售税利(元/件)
- 销售(管)费用(元/件)
- 销售(管)毛利(元/件)

### 其他指标
- 资产减值损失
- 坏账减值损失
- 信用减值损失
- 公允价值变动收益
- 投资收益
- 对联营企业投资收益
- 资产处置收益
- 非流动资产处置利得
- 非流动资产处置损失
- 主要经营指标汇总
- 主要经营指标收支
- 坏账准备增加

## 表结构假设

生成的INSERT语句基于以下表结构：

```sql
CREATE TABLE yjzb_indicator_annual_budget (
    indicator_id BIGINT,           -- 指标ID，关联yjzb_indicator表
    year INT,                      -- 年份
    period_type VARCHAR(50),       -- 期间类型（上半年执行、全年预算、全年调整预算）
    budget_amount DECIMAL(15,2),   -- 预算金额（万元）
    create_time TIMESTAMP          -- 创建时间
);
```

## 使用说明

### 1. 直接使用完整SQL文件
```sql
-- 执行完整的INSERT语句
SOURCE complete_budget_insert_generator.sql;
```

### 2. 处理未匹配指标
对于未匹配的指标，需要：
1. 在yjzb_indicator表中查找是否存在相应指标
2. 如不存在，需要先创建指标记录
3. 获取指标ID后，补充相应的INSERT语句

### 3. 调整表结构
如果实际的yjzb_indicator_annual_budget表结构与假设不同，需要相应调整：
- 字段名称
- 数据类型
- 约束条件

## 注意事项

1. **编码问题**：预算.csv文件存在编码问题，建议转换为UTF-8编码后再处理
2. **数据精度**：金额数据保持原有精度，单位为万元
3. **负值处理**：财务费用等可能为负值的指标已正确处理
4. **空值处理**：对于值为0或"-"的数据，统一处理为0
5. **时间戳**：使用NOW()函数生成创建时间

## 扩展建议

1. **自动化脚本**：可以基于budget_converter.py进一步完善自动化转换脚本
2. **指标补全**：建议补全yjzb_indicator表中缺失的指标
3. **数据验证**：建议添加数据验证逻辑，确保转换结果的准确性
4. **批量处理**：可以扩展为支持多个预算文件的批量处理

## 总结

本次转换成功处理了预算.csv中的主要财务指标，生成了54条INSERT语句（18个指标 × 3个期间）。对于未匹配的指标，需要进一步完善yjzb_indicator表的指标定义，然后补充相应的INSERT语句。
